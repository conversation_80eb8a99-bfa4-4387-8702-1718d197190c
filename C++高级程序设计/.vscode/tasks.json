{"version": "2.0.0", "tasks": [{"label": "编译当前文件", "type": "shell", "command": "g++", "args": ["-g", "-std=c++17", "-Wall", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$gcc"], "detail": "编译当前打开的C++文件"}, {"type": "cppbuild", "label": "C/C++: clang 生成活动文件", "command": "/usr/bin/clang", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}]}