# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/calculator.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/calculator.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/calculator.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/calculator.dir

# All Build rule for target.
CMakeFiles/calculator.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/calculator.dir/build.make CMakeFiles/calculator.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/calculator.dir/build.make CMakeFiles/calculator.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=1,2,3,4 "Built target calculator"
.PHONY : CMakeFiles/calculator.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/calculator.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles 4
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/calculator.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles 0
.PHONY : CMakeFiles/calculator.dir/rule

# Convenience name for target.
calculator: CMakeFiles/calculator.dir/rule
.PHONY : calculator

# codegen rule for target.
CMakeFiles/calculator.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/calculator.dir/build.make CMakeFiles/calculator.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=1,2,3,4 "Finished codegen for target calculator"
.PHONY : CMakeFiles/calculator.dir/codegen

# clean rule for target.
CMakeFiles/calculator.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/calculator.dir/build.make CMakeFiles/calculator.dir/clean
.PHONY : CMakeFiles/calculator.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

