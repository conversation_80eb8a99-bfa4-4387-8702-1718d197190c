# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build

# Include any dependencies generated for this target.
include CMakeFiles/calculator.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/calculator.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/calculator.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/calculator.dir/flags.make

CMakeFiles/calculator.dir/codegen:
.PHONY : CMakeFiles/calculator.dir/codegen

CMakeFiles/calculator.dir/main.cpp.o: CMakeFiles/calculator.dir/flags.make
CMakeFiles/calculator.dir/main.cpp.o: /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/main.cpp
CMakeFiles/calculator.dir/main.cpp.o: CMakeFiles/calculator.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/calculator.dir/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/calculator.dir/main.cpp.o -MF CMakeFiles/calculator.dir/main.cpp.o.d -o CMakeFiles/calculator.dir/main.cpp.o -c /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/main.cpp

CMakeFiles/calculator.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/calculator.dir/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/main.cpp > CMakeFiles/calculator.dir/main.cpp.i

CMakeFiles/calculator.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/calculator.dir/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/main.cpp -o CMakeFiles/calculator.dir/main.cpp.s

CMakeFiles/calculator.dir/calculator.cpp.o: CMakeFiles/calculator.dir/flags.make
CMakeFiles/calculator.dir/calculator.cpp.o: /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/calculator.cpp
CMakeFiles/calculator.dir/calculator.cpp.o: CMakeFiles/calculator.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/calculator.dir/calculator.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/calculator.dir/calculator.cpp.o -MF CMakeFiles/calculator.dir/calculator.cpp.o.d -o CMakeFiles/calculator.dir/calculator.cpp.o -c /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/calculator.cpp

CMakeFiles/calculator.dir/calculator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/calculator.dir/calculator.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/calculator.cpp > CMakeFiles/calculator.dir/calculator.cpp.i

CMakeFiles/calculator.dir/calculator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/calculator.dir/calculator.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/calculator.cpp -o CMakeFiles/calculator.dir/calculator.cpp.s

CMakeFiles/calculator.dir/utils.cpp.o: CMakeFiles/calculator.dir/flags.make
CMakeFiles/calculator.dir/utils.cpp.o: /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/utils.cpp
CMakeFiles/calculator.dir/utils.cpp.o: CMakeFiles/calculator.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/calculator.dir/utils.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/calculator.dir/utils.cpp.o -MF CMakeFiles/calculator.dir/utils.cpp.o.d -o CMakeFiles/calculator.dir/utils.cpp.o -c /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/utils.cpp

CMakeFiles/calculator.dir/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/calculator.dir/utils.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/utils.cpp > CMakeFiles/calculator.dir/utils.cpp.i

CMakeFiles/calculator.dir/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/calculator.dir/utils.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/utils.cpp -o CMakeFiles/calculator.dir/utils.cpp.s

# Object files for target calculator
calculator_OBJECTS = \
"CMakeFiles/calculator.dir/main.cpp.o" \
"CMakeFiles/calculator.dir/calculator.cpp.o" \
"CMakeFiles/calculator.dir/utils.cpp.o"

# External object files for target calculator
calculator_EXTERNAL_OBJECTS =

bin/calculator: CMakeFiles/calculator.dir/main.cpp.o
bin/calculator: CMakeFiles/calculator.dir/calculator.cpp.o
bin/calculator: CMakeFiles/calculator.dir/utils.cpp.o
bin/calculator: CMakeFiles/calculator.dir/build.make
bin/calculator: CMakeFiles/calculator.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable bin/calculator"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/calculator.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/calculator.dir/build: bin/calculator
.PHONY : CMakeFiles/calculator.dir/build

CMakeFiles/calculator.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/calculator.dir/cmake_clean.cmake
.PHONY : CMakeFiles/calculator.dir/clean

CMakeFiles/calculator.dir/depend:
	cd /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/calculator.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/calculator.dir/depend

