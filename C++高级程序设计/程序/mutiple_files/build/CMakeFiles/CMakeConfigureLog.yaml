
---
events:
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:12 (find_program)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_UNAME"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "uname"
    candidate_directories:
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/local/bin/uname"
      - "/System/Cryptexes/App/usr/bin/uname"
    found: "/usr/bin/uname"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Darwin - 24.4.0 - arm64
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeUnixFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gmake"
      - "make"
      - "smake"
    candidate_directories:
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/local/bin/gmake"
      - "/System/Cryptexes/App/usr/bin/gmake"
      - "/usr/bin/gmake"
      - "/bin/gmake"
      - "/usr/sbin/gmake"
      - "/sbin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/gmake"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/gmake"
      - "/Library/Apple/usr/bin/gmake"
      - "/Library/TeX/texbin/gmake"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/gmake"
      - "/opt/homebrew/bin/gmake"
      - "/opt/homebrew/sbin/gmake"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/gmake"
      - "/opt/local/bin/gmake"
      - "/opt/local/sbin/gmake"
      - "/Users/<USER>/.orbstack/bin/gmake"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/gmake"
      - "/usr/local/bin/make"
      - "/System/Cryptexes/App/usr/bin/make"
    found: "/usr/bin/make"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:73 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:64 (_cmake_find_compiler)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER"
    description: "C compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "cc"
      - "gcc"
      - "cl"
      - "bcc"
      - "xlc"
      - "icx"
      - "clang"
    candidate_directories:
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/local/bin/cc"
      - "/System/Cryptexes/App/usr/bin/cc"
    found: "/usr/bin/cc"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: /usr/bin/cc 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out"
      
      The C compiler identification is AppleClang, found in:
        /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/4.1.1/CompilerIdC/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Detecting C compiler apple sysroot: "/usr/bin/cc" "-E" "apple-sdk.c"
        # 1 "apple-sdk.c"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 465 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.c" 2
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 196 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 197 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 33 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 34 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 2 3 4
        # 198 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 34 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 35 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 2 3 4
        # 199 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.c" 2
        
        
      Found apple sysroot: /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/ar"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/ranlib"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/strip"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/ld"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/nm"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    found: "/usr/bin/objdump"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/bin/objcopy"
      - "/usr/local/bin/objcopy"
      - "/System/Cryptexes/App/usr/bin/objcopy"
      - "/bin/objcopy"
      - "/usr/sbin/objcopy"
      - "/sbin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/objcopy"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/objcopy"
      - "/Library/Apple/usr/bin/objcopy"
      - "/Library/TeX/texbin/objcopy"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/objcopy"
      - "/opt/homebrew/bin/objcopy"
      - "/opt/homebrew/sbin/objcopy"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/objcopy"
      - "/opt/local/bin/objcopy"
      - "/opt/local/sbin/objcopy"
      - "/Users/<USER>/.orbstack/bin/objcopy"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/objcopy"
    found: false
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/bin/readelf"
      - "/usr/local/bin/readelf"
      - "/System/Cryptexes/App/usr/bin/readelf"
      - "/bin/readelf"
      - "/usr/sbin/readelf"
      - "/sbin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/readelf"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/readelf"
      - "/Library/Apple/usr/bin/readelf"
      - "/Library/TeX/texbin/readelf"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/readelf"
      - "/opt/homebrew/bin/readelf"
      - "/opt/homebrew/sbin/readelf"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/readelf"
      - "/opt/local/bin/readelf"
      - "/opt/local/sbin/readelf"
      - "/Users/<USER>/.orbstack/bin/readelf"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/readelf"
    found: false
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/bin/dlltool"
      - "/usr/local/bin/dlltool"
      - "/System/Cryptexes/App/usr/bin/dlltool"
      - "/bin/dlltool"
      - "/usr/sbin/dlltool"
      - "/sbin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/dlltool"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/dlltool"
      - "/Library/Apple/usr/bin/dlltool"
      - "/Library/TeX/texbin/dlltool"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/dlltool"
      - "/opt/homebrew/bin/dlltool"
      - "/opt/homebrew/sbin/dlltool"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/dlltool"
      - "/opt/local/bin/dlltool"
      - "/opt/local/sbin/dlltool"
      - "/Users/<USER>/.orbstack/bin/dlltool"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/dlltool"
    found: false
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/bin/addr2line"
      - "/usr/local/bin/addr2line"
      - "/System/Cryptexes/App/usr/bin/addr2line"
      - "/bin/addr2line"
      - "/usr/sbin/addr2line"
      - "/sbin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/addr2line"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/addr2line"
      - "/Library/Apple/usr/bin/addr2line"
      - "/Library/TeX/texbin/addr2line"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/addr2line"
      - "/opt/homebrew/bin/addr2line"
      - "/opt/homebrew/sbin/addr2line"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/addr2line"
      - "/opt/local/bin/addr2line"
      - "/opt/local/sbin/addr2line"
      - "/Users/<USER>/.orbstack/bin/addr2line"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/addr2line"
    found: false
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "/usr/bin/"
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/bin/tapi"
      - "/usr/local/bin/tapi"
      - "/System/Cryptexes/App/usr/bin/tapi"
      - "/bin/tapi"
      - "/usr/sbin/tapi"
      - "/sbin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/tapi"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/tapi"
      - "/Library/Apple/usr/bin/tapi"
      - "/Library/TeX/texbin/tapi"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/tapi"
      - "/opt/homebrew/bin/tapi"
      - "/opt/homebrew/sbin/tapi"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/tapi"
      - "/opt/local/bin/tapi"
      - "/opt/local/sbin/tapi"
      - "/Users/<USER>/.orbstack/bin/tapi"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompiler.cmake:54 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:69 (_cmake_find_compiler)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER"
    description: "CXX compiler"
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "c++"
      - "g++"
      - "cl"
      - "bcc"
      - "icpx"
      - "icx"
      - "clang++"
    candidate_directories:
      - "/usr/bin/"
    found: "/usr/bin/c++"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "/opt/homebrew/share/cmake/Modules/"
    found: "/opt/homebrew/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: /usr/bin/c++ 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out"
      
      The CXX compiler identification is AppleClang, found in:
        /Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/4.1.1/CompilerIdCXX/a.out
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerId.cmake:290 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Detecting CXX compiler apple sysroot: "/usr/bin/c++" "-E" "apple-sdk.cpp"
        # 1 "apple-sdk.cpp"
        # 1 "<built-in>" 1
        # 1 "<built-in>" 3
        # 513 "<built-in>" 3
        # 1 "<command line>" 1
        # 1 "<built-in>" 2
        # 1 "apple-sdk.cpp" 2
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 1 3 4
        # 89 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 90 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/TargetConditionals.h" 1 3 4
        # 91 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 207 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 1 3 4
        # 196 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 197 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 33 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityVersions.h" 1 3 4
        # 34 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 2 3 4
        # 198 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 1 3 4
        # 34 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 3 4
        # 1 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternal.h" 1 3 4
        # 35 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityInternalLegacy.h" 2 3 4
        # 199 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/Availability.h" 2 3 4
        # 208 "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/AvailabilityMacros.h" 2 3 4
        # 2 "apple-sdk.cpp" 2
        
        
      Found apple sysroot: /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk
  -
    kind: "find-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Platform/Darwin.cmake:76 (find_program)"
      - "/opt/homebrew/share/cmake/Modules/CMakeSystemSpecificInformation.cmake:32 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_INSTALL_NAME_TOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "FIRST"
      SearchAppBundle: "FIRST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "install_name_tool"
    candidate_directories:
      - "/usr/local/bin/"
      - "/System/Cryptexes/App/usr/bin/"
      - "/usr/bin/"
      - "/bin/"
      - "/usr/sbin/"
      - "/sbin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin/"
      - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin/"
      - "/Library/Apple/usr/bin/"
      - "/Library/TeX/texbin/"
      - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin/"
      - "/opt/homebrew/bin/"
      - "/opt/homebrew/sbin/"
      - "/Library/Frameworks/Python.framework/Versions/3.13/bin/"
      - "/opt/local/bin/"
      - "/opt/local/sbin/"
      - "/Users/<USER>/.orbstack/bin/"
      - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts/"
    searched_directories:
      - "/usr/local/bin/install_name_tool"
      - "/System/Cryptexes/App/usr/bin/install_name_tool"
    found: "/usr/bin/install_name_tool"
    search_context:
      ENV{PATH}:
        - "/usr/local/bin"
        - "/System/Cryptexes/App/usr/bin"
        - "/usr/bin"
        - "/bin"
        - "/usr/sbin"
        - "/sbin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin"
        - "/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin"
        - "/Library/Apple/usr/bin"
        - "/Library/TeX/texbin"
        - "/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_452/Contents/Home/bin"
        - "/opt/homebrew/bin"
        - "/opt/homebrew/sbin"
        - "/Library/Frameworks/Python.framework/Versions/3.13/bin"
        - "/opt/local/bin"
        - "/opt/local/sbin"
        - "/Users/<USER>/.orbstack/bin"
        - "/Users/<USER>/.vscode/extensions/ms-python.debugpy-2025.10.0/bundled/scripts/noConfigScripts"
      CMAKE_INSTALL_PREFIX: "/usr/local"
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "/Volumes/\u674e\u7136\u7684\u786c\u76d8/\u5927\u4e8c\u4e0b\u5b66\u671f\u8bfe\u7a0b/C++\u9ad8\u7ea7\u7a0b\u5e8f\u8bbe\u8ba1/\u7a0b\u5e8f/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc"
      binary: "/Volumes/\u674e\u7136\u7684\u786c\u76d8/\u5927\u4e8c\u4e0b\u5b66\u671f\u8bfe\u7a0b/C++\u9ad8\u7ea7\u7a0b\u5e8f\u8bbe\u8ba1/\u7a0b\u5e8f/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6f809/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_6f809.dir/build.make CMakeFiles/cmTC_6f809.dir/build
        Building C object CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o
        /usr/bin/cc   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.4.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/17/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c
        clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.4.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Library/Developer/CommandLineTools/usr/lib/clang/17/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking C executable cmTC_6f809
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6f809.dir/link.txt --verbose=1
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.4.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_6f809 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/usr/local/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/cc  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -o cmTC_6f809
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/17/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/17/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/17/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/usr/local/include;/Library/Developer/CommandLineTools/usr/lib/clang/17/include;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_6f809/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_6f809.dir/build.make CMakeFiles/cmTC_6f809.dir/build]
        ignore line: [Building C object CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o]
        ignore line: [/usr/bin/cc   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -MF CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o.d -o CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.4.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-ti17Vc -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/17/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fskip-odr-check-in-gmf -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -x c /opt/homebrew/share/cmake/Modules/CMakeCCompilerABI.c]
        ignore line: [clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.4.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/17/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking C executable cmTC_6f809]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_6f809.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.4.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_6f809 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_6f809] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_6f809.dir/CMakeCCompilerABI.c.o] ==> ignore
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        linker tool for 'C': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: []
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the C compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
  -
    kind: "try_compile-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "/Volumes/\u674e\u7136\u7684\u786c\u76d8/\u5927\u4e8c\u4e0b\u5b66\u671f\u8bfe\u7a0b/C++\u9ad8\u7ea7\u7a0b\u5e8f\u8bbe\u8ba1/\u7a0b\u5e8f/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ"
      binary: "/Volumes/\u674e\u7136\u7684\u786c\u76d8/\u5927\u4e8c\u4e0b\u5b66\u671f\u8bfe\u7a0b/C++\u9ad8\u7ea7\u7a0b\u5e8f\u8bbe\u8ba1/\u7a0b\u5e8f/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_OSX_ARCHITECTURES: ""
      CMAKE_OSX_DEPLOYMENT_TARGET: ""
      CMAKE_OSX_SYSROOT: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: '/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ'
        
        Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7ebf0/fast
        /Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_7ebf0.dir/build.make CMakeFiles/cmTC_7ebf0.dir/build
        Building CXX object CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o
        /usr/bin/c++   -arch arm64   -v -Wl,-v -MD -MT CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.4.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
        clang++: warning: -Wl,-v: 'linker' input unused [-Wunused-command-line-argument]
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"
         "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/17/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp
        clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.4.0
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/SubFrameworks"
        ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"
        #include "..." search starts here:
        #include <...> search starts here:
         /usr/local/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1
         /Library/Developer/CommandLineTools/usr/lib/clang/17/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include
         /Library/Developer/CommandLineTools/usr/include
         /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)
        End of search list.
        Linking CXX executable cmTC_7ebf0
        /opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7ebf0.dir/link.txt --verbose=1
        Apple clang version 17.0.0 (clang-1700.0.13.5)
        Target: arm64-apple-darwin24.4.0
        Thread model: posix
        InstalledDir: /Library/Developer/CommandLineTools/usr/bin
         "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_7ebf0 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a
        @(#)PROGRAM:ld PROJECT:ld-1167.5
        BUILD 01:45:05 Apr 30 2025
        configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
        will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
        LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
        TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
        Library search paths:
        	/usr/local/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift
        Framework search paths:
        	/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks
        /usr/bin/c++  -arch arm64 -Wl,-search_paths_first -Wl,-headerpad_max_install_names  -v -Wl,-v CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -o cmTC_7ebf0
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:122 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Effective list of requested architectures (possibly empty)  : ""
      Effective list of architectures found in the ABI info binary: "arm64"
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [/usr/local/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
          add: [/Library/Developer/CommandLineTools/usr/lib/clang/17/include]
          add: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
          add: [/Library/Developer/CommandLineTools/usr/include]
        end of search list found
        collapse include dir [/usr/local/include] ==> [/usr/local/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        collapse include dir [/Library/Developer/CommandLineTools/usr/lib/clang/17/include] ==> [/Library/Developer/CommandLineTools/usr/lib/clang/17/include]
        collapse include dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        collapse include dir [/Library/Developer/CommandLineTools/usr/include] ==> [/Library/Developer/CommandLineTools/usr/include]
        implicit include dirs: [/usr/local/include;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1;/Library/Developer/CommandLineTools/usr/lib/clang/17/include;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include;/Library/Developer/CommandLineTools/usr/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: '/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ']
        ignore line: []
        ignore line: [Run Build Command(s): /opt/homebrew/bin/cmake -E env VERBOSE=1 /usr/bin/make -f Makefile cmTC_7ebf0/fast]
        ignore line: [/Library/Developer/CommandLineTools/usr/bin/make  -f CMakeFiles/cmTC_7ebf0.dir/build.make CMakeFiles/cmTC_7ebf0.dir/build]
        ignore line: [Building CXX object CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o]
        ignore line: [/usr/bin/c++   -arch arm64   -v -Wl -v -MD -MT CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -MF CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o.d -o CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -c /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.4.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        ignore line: [clang++: warning: -Wl -v: 'linker' input unused [-Wunused-command-line-argument]]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"]
        ignore line: [ "/Library/Developer/CommandLineTools/usr/bin/clang" -cc1 -triple arm64-apple-macosx15.0.0 -Wundef-prefix=TARGET_OS_ -Wdeprecated-objc-isa-usage -Werror=deprecated-objc-isa-usage -Werror=implicit-function-declaration -emit-obj -disable-free -clear-ast-before-backend -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -mframe-pointer=non-leaf -fno-strict-return -ffp-contract=on -fno-rounding-math -funwind-tables=1 -fobjc-msgsend-selector-stubs -target-sdk-version=15.5 -fvisibility-inlines-hidden-static-local-var -fdefine-target-os-macros -fno-assume-unique-vtables -fno-modulemap-allow-subdirectory-search -target-cpu apple-m1 -target-feature +zcm -target-feature +zcz -target-feature +v8.5a -target-feature +aes -target-feature +altnzcv -target-feature +ccdp -target-feature +complxnum -target-feature +crc -target-feature +dotprod -target-feature +fp-armv8 -target-feature +fp16fml -target-feature +fptoint -target-feature +fullfp16 -target-feature +jsconv -target-feature +lse -target-feature +neon -target-feature +pauth -target-feature +perfmon -target-feature +predres -target-feature +ras -target-feature +rcpc -target-feature +rdm -target-feature +sb -target-feature +sha2 -target-feature +sha3 -target-feature +specrestrict -target-feature +ssbs -target-abi darwinpcs -debugger-tuning=lldb -fdebug-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ -target-linker-version 1167.5 -v -fcoverage-compilation-dir=/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/mutiple_files/build/CMakeFiles/CMakeScratch/TryCompile-HPjckJ -resource-dir /Library/Developer/CommandLineTools/usr/lib/clang/17 -dependency-file CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o.d -skip-unused-modulemap-deps -MT CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -isysroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -I/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1 -internal-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include -internal-isystem /Library/Developer/CommandLineTools/usr/lib/clang/17/include -internal-externc-isystem /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include -internal-externc-isystem /Library/Developer/CommandLineTools/usr/include -Wno-reorder-init-list -Wno-implicit-int-float-conversion -Wno-c99-designator -Wno-final-dtor-non-final-class -Wno-extra-semi-stmt -Wno-misleading-indentation -Wno-quoted-include-in-framework-header -Wno-implicit-fallthrough -Wno-enum-enum-conversion -Wno-enum-float-conversion -Wno-elaborated-enum-base -Wno-reserved-identifier -Wno-gnu-folding-constant -fdeprecated-macro -ferror-limit 19 -stack-protector 1 -fstack-check -mdarwin-stkchk-strong-link -fblocks -fencode-extended-block-signature -fregister-global-dtors-with-atexit -fgnuc-version=4.2.1 -fno-cxx-modules -fskip-odr-check-in-gmf -fcxx-exceptions -fexceptions -fmax-type-align=16 -fcommon -clang-vendor-feature=+disableNonDependentMemberExprInCurrentInstantiation -fno-odr-hash-protocols -clang-vendor-feature=+enableAggressiveVLAFolding -clang-vendor-feature=+revert09abecef7bbf -clang-vendor-feature=+thisNoAlignAttr -clang-vendor-feature=+thisNoNullAttr -clang-vendor-feature=+disableAtImportPrivateFrameworkInImplementationError -D__GCC_HAVE_DWARF2_CFI_ASM=1 -o CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -x c++ /opt/homebrew/share/cmake/Modules/CMakeCXXCompilerABI.cpp]
        ignore line: [clang -cc1 version 17.0.0 (clang-1700.0.13.5) default target arm64-apple-darwin24.4.0]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/local/include"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/SubFrameworks"]
        ignore line: [ignoring nonexistent directory "/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/Library/Frameworks"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ /usr/local/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include/c++/v1]
        ignore line: [ /Library/Developer/CommandLineTools/usr/lib/clang/17/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/usr/include]
        ignore line: [ /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks (framework directory)]
        ignore line: [End of search list.]
        ignore line: [Linking CXX executable cmTC_7ebf0]
        ignore line: [/opt/homebrew/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7ebf0.dir/link.txt --verbose=1]
        ignore line: [Apple clang version 17.0.0 (clang-1700.0.13.5)]
        ignore line: [Target: arm64-apple-darwin24.4.0]
        ignore line: [Thread model: posix]
        ignore line: [InstalledDir: /Library/Developer/CommandLineTools/usr/bin]
        link line: [ "/Library/Developer/CommandLineTools/usr/bin/ld" -demangle -lto_library /Library/Developer/CommandLineTools/usr/lib/libLTO.dylib -dynamic -arch arm64 -platform_version macos 15.0.0 15.5 -syslibroot /Library/Developer/CommandLineTools/SDKs/MacOSX.sdk -mllvm -enable-linkonceodr-outlining -o cmTC_7ebf0 -L/usr/local/lib -search_paths_first -headerpad_max_install_names -v CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o -lc++ -lSystem /Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
          arg [/Library/Developer/CommandLineTools/usr/bin/ld] ==> ignore
          arg [-demangle] ==> ignore
          arg [-lto_library] ==> ignore, skip following value
          arg [/Library/Developer/CommandLineTools/usr/lib/libLTO.dylib] ==> skip value of -lto_library
          arg [-dynamic] ==> ignore
          arg [-arch] ==> ignore
          arg [arm64] ==> ignore
          arg [-platform_version] ==> ignore
          arg [macos] ==> ignore
          arg [15.0.0] ==> ignore
          arg [15.5] ==> ignore
          arg [-syslibroot] ==> ignore
          arg [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk] ==> ignore
          arg [-mllvm] ==> ignore
          arg [-enable-linkonceodr-outlining] ==> ignore
          arg [-o] ==> ignore
          arg [cmTC_7ebf0] ==> ignore
          arg [-L/usr/local/lib] ==> dir [/usr/local/lib]
          arg [-search_paths_first] ==> ignore
          arg [-headerpad_max_install_names] ==> ignore
          arg [-v] ==> ignore
          arg [CMakeFiles/cmTC_7ebf0.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
          arg [-lc++] ==> lib [c++]
          arg [-lSystem] ==> lib [System]
          arg [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a] ==> lib [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        linker tool for 'CXX': /Library/Developer/CommandLineTools/usr/bin/ld
        Library search paths: [;/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        Framework search paths: [;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        remove lib [System]
        remove lib [/Library/Developer/CommandLineTools/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/usr/local/lib] ==> [/usr/local/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib]
        collapse library dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        collapse framework dir [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks] ==> [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
        implicit libs: [c++]
        implicit objs: []
        implicit dirs: [/usr/local/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib;/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/usr/lib/swift]
        implicit fwks: [/Library/Developer/CommandLineTools/SDKs/MacOSX.sdk/System/Library/Frameworks]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "/opt/homebrew/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "/opt/homebrew/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "/opt/homebrew/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the CXX compiler's linker: "/Library/Developer/CommandLineTools/usr/bin/ld" "-v"
      @(#)PROGRAM:ld PROJECT:ld-1167.5
      BUILD 01:45:05 Apr 30 2025
      configured to support archs: armv6 armv7 armv7s arm64 arm64e arm64_32 i386 x86_64 x86_64h armv6m armv7k armv7m armv7em
      will use ld-classic for: armv6 armv7 armv7s i386 armv6m armv7k armv7m armv7em
      LTO support using: LLVM version 17.0.0 (static support for 29, runtime is 29)
      TAPI support using: Apple TAPI version 17.0.0 (tapi-1700.0.3.5)
...
