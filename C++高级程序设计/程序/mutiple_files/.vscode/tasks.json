{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: clang++ 生成多文件程序", "command": "/usr/bin/clang++", "args": ["-std=c++11", "-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${workspaceFolder}/main.cpp", "${workspaceFolder}/calculator.cpp", "${workspaceFolder}/utils.cpp", "-o", "${workspaceFolder}/calculator"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "编译多文件C++程序"}]}