{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: 编译所有cpp文件", "command": "/usr/bin/clang++", "args": ["-std=c++11", "-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${workspaceFolder}/*.cpp", "-o", "${workspaceFolder}/program"], "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "自动编译当前目录下所有cpp文件"}, {"type": "shell", "label": "使用Makefile编译", "command": "make", "group": "build", "detail": "使用Makefile编译项目"}, {"type": "shell", "label": "使用CMake编译", "command": "cmake", "args": ["--build", "build"], "group": "build", "detail": "使用CMake编译项目"}]}