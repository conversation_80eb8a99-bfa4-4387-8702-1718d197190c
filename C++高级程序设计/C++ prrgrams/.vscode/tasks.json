{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: clang++ build active file", "command": "/usr/bin/clang++", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "-std=c++11", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}]}