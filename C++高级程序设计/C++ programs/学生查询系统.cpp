#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
using namespace std;

struct Student {
    int    id;
    string name;
    int    age;
    char   gender;
    string birthPlace;
};

int rulesCount, currentSortRules[6];

// TODO: 完成比较函数 globalCompare

// TODO: 完成排序函数 sortStudents

int main() {
    int n, m;

    cin >> n >> m;
    Student students[50];
    for (int i = 0; i < n; i++) {
        cin >> students[i].id >> students[i].name >> students[i].age >> students[i].gender >> students[i].birthPlace;
    }

    while (m--) {
        cin >> rulesCount;
        for (int i = 0; i < rulesCount; i++) {
            cin >> currentSortRules[i];
        }
        
        sortStudents(students, n, globalCompare);

        // 输出排序后的结果
        for (int i = 0; i < n; i++) {
            cout << students[i].id<< endl;
        }
    }

    return 0;
}