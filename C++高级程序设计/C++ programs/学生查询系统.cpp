#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
using namespace std;

struct Student {
    int    id;
    string name;
    int    age;
    char   gender;
    string birthPlace;
};

int rulesCount, currentSortRules[6];

// TODO: 完成比较函数 globalCompare

// TODO: 完成排序函数 sortStudents
enum type {
    one,
    two,
    none
};
bool globalCompare ( const Student a, const Student b  ) {

    for ( int i=0;i<rulesCount;i++) {
        switch ( currentSortRules[i]  ) {
            case 1:
                
        }
    }

return ;
}

void sortStudents(Student students[],int n) {
    Student sorted[n];
    sorted[0] = students[0];
    for ( int i=1; i<n;i++) {
        if ( globalCompare(sorted[i-1],students[i] ) ) {
            sorted[i] = students[i];
            continue;
        }
        for ( int j= 0 ; j < i ; j++ ) {
            if (globalCompare(students[i],sorted[j]) ) {//if the first one is smaller, then move the sorted sequence and insert the new one .
                for ( int k = i ; k >j ; k--) {
                    sorted[k] = sorted[k-1];
                }
                sorted[j] = students[i];

            }

        }

    }



}

int main() {
    int n, m;

    cin >> n >> m;
    Student students[50];
    for (int i = 0; i < n; i++) {
        cin >> students[i].id >> students[i].name >> students[i].age >> students[i].gender >> students[i].birthPlace;
    }

    while (m--) {
        cin >> rulesCount;
        for (int i = 0; i < rulesCount; i++) {
            cin >> currentSortRules[i];
        }
        
        sortStudents(students, n);

        // 输出排序后的结果
        for (int i = 0; i < n; i++) {
            cout << students[i].id<< endl;
        }
    }

    return 0;
}