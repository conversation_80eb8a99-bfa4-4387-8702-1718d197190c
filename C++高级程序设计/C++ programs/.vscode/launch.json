{"version": "0.2.0", "configurations": [{"name": "C/C++: clang++ build and debug active file", "type": "cppdbg", "request": "launch", "program": "${fileDirname}/${fileBasenameNoExtension}", "args": [], "stopAtEntry": false, "cwd": "${fileDirname}", "environment": [], "externalConsole": false, "MIMode": "lldb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "C/C++: clang++ build active file", "miDebuggerPath": "/usr/bin/lldb-mi"}]}