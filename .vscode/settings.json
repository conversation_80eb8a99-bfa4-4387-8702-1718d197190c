{"files.associations": {"__locale": "cpp", "__split_buffer": "cpp", "__verbose_abort": "cpp", "array": "cpp", "bitset": "cpp", "cmath": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "deque": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "locale": "cpp", "mutex": "cpp", "new": "cpp", "print": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "vector": "cpp", "__bit_reference": "cpp", "__hash_table": "cpp", "__node_handle": "cpp", "cctype": "cpp", "clocale": "cpp", "complex": "cpp", "cstdarg": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "execution": "cpp", "memory": "cpp", "optional": "cpp", "queue": "cpp", "ratio": "cpp", "sstream": "cpp", "string": "cpp", "string_view": "cpp", "variant": "cpp", "algorithm": "cpp"}, "cmake.sourceDirectory": "/Volumes/李然的硬盘/大二下学期课程/C++高级程序设计/程序/1.hello world"}