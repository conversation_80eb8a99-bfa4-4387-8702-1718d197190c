{"tasks": [{"type": "cppbuild", "label": "C/C++: clang 生成活动文件", "command": "/usr/bin/clang", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: clang 生成活动文件", "command": "/usr/bin/clang", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "编译器: /usr/bin/clang"}], "version": "2.0.0"}